# 订单列表邮箱搜索功能实现

## 问题解决

原始实现中遇到了 Tortoise ORM 的字段解析错误：
```
tortoise.exceptions.FieldError: There is no non-virtual field user__email on Model User
```

## 解决方案

采用两步查询的方式来避免 Tortoise ORM 的关联字段查询问题：

1. **第一步**：通过用户表查找匹配邮箱的用户ID
2. **第二步**：使用用户ID列表查询订单

## 后端实现

### 1. 修改订单查询方法 (`backend/apps/membership/curd.py`)

```python
@staticmethod
async def get_order_list(page: int, page_size: int = 20, user_id: int = None, search: str = ""):
    """Get order list"""
    offset = (page - 1) * page_size

    # 如果有搜索条件，先通过用户表查找匹配的用户ID
    user_ids = None
    if search:
        users = await User.filter(email__icontains=search).values_list('id', flat=True)
        user_ids = list(users)
        if not user_ids:
            # 如果没有匹配的用户，返回空结果
            return [], 0

    # 构建订单查询
    query_set = MemberOrder.all().select_related("plan", "user")

    if user_id:
        query_set = query_set.filter(user_id=user_id, status=OrderStatusEnum.PAID)
    elif user_ids is not None:
        query_set = query_set.filter(user_id__in=user_ids)

    # 添加 email 注解
    query_set = query_set.annotate(email=F("user__email"))

    total = await query_set.count()
    data_list = await query_set.offset(offset).limit(page_size).order_by("-id")
    return data_list, total
```

### 2. 修改管理员接口 (`backend/apps/membership/admin.py`)

```python
async def order_list(
    page: int = 1, 
    page_size: int = 20,
    search: str = Query(default="", description="搜索用户邮箱")
):
    """
    订单列表
    """
    data_list, total = await MemberOrderCRUD.get_order_list(page, page_size, search=search)
    data_list = [
        MemberOrderWithPlan.model_validate(order, strict=False) for order in data_list
    ]
    return resp_200(data=data_list, total=total, page=page, page_size=page_size)
```

## 前端实现

### 1. 添加搜索框 (`frontend/src/views/admin/Order/index.vue`)

```vue
<div class="table-operations">
    <a-space>
        <a-input-search
            placeholder="请输入用户邮箱"
            enter-button="搜索"
            v-model:value="params.search"
            @search="onSearch"
            @change="onSearchChange"
            style="width: 300px"
        />
    </a-space>
</div>
```

### 2. 添加搜索逻辑

```javascript
const params = ref({
    page: 1,
    page_size: pagination.value.pageSize,
    search: ''
})

// 搜索相关函数
const onSearchChange = (e) => {
    params.value.search = e?.target?.value || '';
};

const onSearch = () => {
    params.value.page = 1;
    pagination.value.current = 1;
    getList();
};
```

## 功能特点

1. **模糊搜索**：支持邮箱部分内容匹配
2. **性能优化**：两步查询避免复杂的关联查询
3. **错误处理**：当没有匹配用户时返回空结果
4. **分页重置**：搜索时自动重置到第一页

## 测试方法

1. 启动后端服务
2. 访问管理员订单列表页面
3. 在搜索框中输入用户邮箱（完整或部分）
4. 点击搜索按钮或按回车键
5. 验证搜索结果是否正确

## API 接口

```
GET /api/v1/admin/orders?search=<EMAIL>&page=1&page_size=20
```

参数说明：
- `search`: 用户邮箱搜索关键词（可选）
- `page`: 页码（默认1）
- `page_size`: 每页数量（默认20）
