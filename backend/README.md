# 说明文档

## docker 启动数据库

### 数据库配置在/db/database.py

```shell
docker run --rm -d --name demo-mysql -e MYSQL_ROOT_PASSWORD=docker -e MYSQL_DATABASE=test -p 3307:3306 mysql:8.2.0
```

### 安装依赖

```shell
python  3.10.6

pip install -r requirements.txt
```

### 数据库迁移

```shell
# Initialize the config file and migrations location:
aerich init -t db.database.TORTOISE_ORM_MIGRATE
# (初始化db)
aerich init-db
aerich migrate --name xxx
aerich upgrade
# 数据库字段更新时执行，生成新的migration文件 (更新数据库表)
python3 scripts/migrate.py
```

### 启动服务

```shell
python main.py
# 后台启动
nohup python main.py &
```

### 初始化数据

```shell
# admin / admin
python scripts/init_data.py 
```

### 单测

```shell
pytest
```

### 线上环境，可以设置.env 对应的环境变量

```shell
cp example.env .env
```

### 接口说明

```shell
请求头里面设置
'Authorization': 'Bearer token'

所有接口
http status code 200
response:
{
  "code": 200 # 成功 其他值 不成功
} 

token过期和没有token  响应 status code  401 
没有权限              响应   status code  403

```

## 部署方案

### Gunicorn+Uvicorn

- 安装依赖

```shell
pip install gunicorn uvicorn[standard]
```

### 启动服务

```shell
gunicorn -w 4 -b 127.0.0.1:8010 -k uvicorn.workers.UvicornWorker main:app
```


### Nginx
```conf
server {
    listen 80;
    server_name your_domain.com;
    # 重定向 HTTP 到 HTTPS
    return 301 https://$host$request_uri;
}
server {
    
    listen 443 ssl http2;
    server_name your_domain.com;
    # SSL 证书配置
    ssl_certificate /etc/letsencrypt/live/your_domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your_domain.com/privkey.pem;
    
    # 性能优化
    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

    # 前端静态文件配置（假设前端编译后文件在 /var/www/frontend）
    location / {
        root /var/www/frontend/dist;  # 修改为你的前端文件实际路径
        index index.html;
        try_files $uri $uri/ /index.html;  # 支持前端路由（如 Vue Router 的 history 模式）
        
        # 静态文件缓存
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }

    # 后端 API 配置（代理到 FastAPI）
    location /api {
        proxy_pass http://localhost:8010;  # 确保端口与 FastAPI 服务一致
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 增加超时和缓冲区设置
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
        proxy_buffer_size 128k;
        proxy_buffers 4 256k;
    }

    # 禁止访问隐藏文件
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
}

```