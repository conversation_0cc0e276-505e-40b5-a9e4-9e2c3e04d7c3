from pydantic import BaseModel
from typing import List, Optional
from core.base_schemas import BaseListOut, BaseOut
from tortoise.contrib.pydantic import pydantic_model_creator
from models.base import MemberOrder, MembershipPlan
from core.base_schemas import base_exclude


class OrderInfo(BaseModel):
    plan_id: int
    pay_channel: str


class OrderStatus(BaseModel):
    id: int
    status: str


class OrderQRCode(BaseModel):
    order_id: int
    order_no: str
    QRcode_url: str


class OrderQRCodeOut(BaseOut):
    data: OrderQRCode


class OrderStatusOut(BaseOut):
    data: OrderStatus


# 创建会员计划的 pydantic 模型
MembershipPlanInDetail = pydantic_model_creator(
    MembershipPlan,
    name="MembershipPlanInDetail",
    include=["id", "name", "duration_days", "original_price", "discount_price"],
)

# 创建订单的 pydantic 模型，包含关联的会员计划信息
MemberOrderInDetail = pydantic_model_creator(
    MemberOrder,
    name="MemberOrderInDetail",
    include=[
        "id",
        "order_no",
        "original_amount",
        "discount_amount",
        "actual_amount",
        "status",
        "pay_time",
        "pay_channel",
        "transaction_id",
        "create_time",
        "duration_days",
    ],
)


# 扩展订单模型以包含会员计划信息
class MemberOrderWithPlan(MemberOrderInDetail):
    plan: MembershipPlanInDetail  #  type: ignore
    email: str


class MemberOrderList(BaseListOut):
    data: List[MemberOrderWithPlan]  #


MembershipPlanCreate = pydantic_model_creator(
    MembershipPlan, name="MembershipPlanCreate", exclude=base_exclude
)


MembershipPlanUpdate = pydantic_model_creator(
    MembershipPlan, name="MembershipPlanUpdate", exclude=("create_time", "update_time")
)

MembershipPlanInfo = pydantic_model_creator(MembershipPlan, name="MembershipPlanInfo")


class MembershipPlanOut(BaseOut):
    """Schema for membership plan response"""

    data: MembershipPlanInfo  # type: ignore


class MembershipPlanList(BaseListOut):
    """Schema for membership plan list response"""

    data: List[MembershipPlanInfo]  # type: ignore


class GrantMembershipDuration(BaseModel):
    user_id: int
    plan_id: int
