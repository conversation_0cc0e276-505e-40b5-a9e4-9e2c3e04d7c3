import aiohttp
import time
import hashlib
from typing import Optional, Dict, Any
from dataclasses import dataclass


@dataclass
class WxPayParams:
    mch_id: str
    out_trade_no: str
    total_fee: float
    body: str
    notify_url: str
    attach: Optional[str] = None
    time_expire: Optional[str] = None
    developer_appid: Optional[str] = None


class PaymentUtils:
    BASE_URL = "https://api.ltzf.cn"
    # 这里需要设置你的商户密钥
    MERCHANT_KEY = "9154e3e2dbcce4b85862274bb04a5375"

    @staticmethod
    def wx_pay_sign(
        params: Dict[str, Any],
        key: str,
        sign_key=[
            "mch_id",
            "out_trade_no",
            "total_fee",
            "body",
            "timestamp",
            "notify_url",
        ],
    ) -> str:
        """
        计算微信支付签名

        Args:
            params: 需要签名的参数字典
            key: 商户密钥

        Returns:
            str: MD5签名结果（大写）
        """
        # 过滤并排序有效参数
        valid_params = sorted(
            (k, str(v))
            for k, v in params.items()
            if k in sign_key and v is not None and v != ""
        )

        # 拼接参数和密钥
        sign_string = "&".join(f"{k}={v}" for k, v in valid_params) + f"&key={key}"

        # 计算MD5并转大写
        return hashlib.md5(sign_string.encode("utf-8")).hexdigest().upper()

    @staticmethod
    async def generate_wx_pay_qrcode(params: WxPayParams) -> dict:
        """
        异步生成微信支付二维码

        Args:
            params: WxPayParams对象，包含支付所需的参数

        Returns:
            dict: 支付接口返回的数据
        """
        # 构建请求数据
        payload = {
            "mch_id": "1718247984",
            "out_trade_no": params.out_trade_no,
            "total_fee": str(params.total_fee),
            "body": params.body,
            "timestamp": str(int(time.time())),
            "notify_url": params.notify_url,
            "attach": params.attach or "自定义数据",
            "time_expire": params.time_expire,
            "developer_appid": params.developer_appid,
        }

        # 计算签名
        payload["sign"] = PaymentUtils.wx_pay_sign(payload, PaymentUtils.MERCHANT_KEY)
        headers = {"content-type": "application/x-www-form-urlencoded"}

        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{PaymentUtils.BASE_URL}/api/wxpay/native",
                    data=payload,
                    headers=headers,
                ) as response:
                    result = await response.json()
                    print(result)
                    return result
        except Exception as e:
            raise Exception(f"生成支付二维码失败: {str(e)}")


if __name__ == "__main__":
    params = WxPayParams(
        mch_id="1718247984",
        out_trade_no="MO2025052820464500021883",
        total_fee=str("0.01"),
        body="月卡",
        notify_url="http://api.xiaobinai.top/api/v1/membership/order/callback",
        attach=None,
        time_expire=None,
        developer_appid=None,
    )
    payload = {
        "mch_id": "1718247984",
        "out_trade_no": params.out_trade_no,
        "total_fee": str(params.total_fee),
        "body": params.body,
        "timestamp": str(int(time.time())),
        "notify_url": params.notify_url,
        "attach": params.attach or "自定义数据",
        "time_expire": params.time_expire,
        "developer_appid": params.developer_appid,
    }
    import asyncio

    asyncio.run(PaymentUtils.generate_wx_pay_qrcode(params))
