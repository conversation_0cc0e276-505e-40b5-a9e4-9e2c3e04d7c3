from fastapi import APIRouter, Depends, Form, Body, File, UploadFile
from core.base_response import resp_200
from core.base_schemas import BaseOut
from starlette.responses import JSONResponse, PlainTextResponse, Response
from typing import Optional, Dict
from fastapi import Request
from pydantic import BaseModel
from loguru import logger
from core.security import verify_user_login
from .curd import MembershipCRUD, MemberOrderCRUD, MembershipPlanCRUD
from .schema import (
    OrderInfo,
    OrderStatusOut,
    OrderStatus,
    OrderQRCodeOut,
    MembershipPlanInfo,
    MemberOrderWithPlan,
)

router = APIRouter()


class CallbackPayload(BaseModel):
    code: Optional[str] = None
    timestamp: Optional[str] = None
    mch_id: Optional[str] = None
    order_no: Optional[str] = None
    out_trade_no: Optional[str] = None
    pay_no: Optional[str] = None
    total_fee: Optional[str] = None


# 试用
@router.post("/membership/trial", response_model=BaseOut, description="试用")
async def trial_membership(token=Depends(verify_user_login)):
    """
    激活试用
    """
    await MembershipCRUD.activate_trial(token.uid)
    return resp_200()


# 生成订单
@router.post("/membership/order", response_model=OrderQRCodeOut, description="生成订单")
async def create_order(order_info: OrderInfo, token=Depends(verify_user_login)):
    """
    生成订单
    """
    order = await MemberOrderCRUD.create_order(
        token.uid, order_info.pay_channel, order_info.plan_id
    )
    return resp_200(data=order)


# 检查支付状态
@router.get(
    "/membership/order/check", response_model=OrderStatusOut, description="检查支付状态"
)
async def check_order_status(order_id: int, token=Depends(verify_user_login)):
    """
    检查支付状态
    """
    order = await MembershipCRUD.get_order(order_id)
    return resp_200(data=OrderStatus(id=order.id, status=order.status))


# 支付回调
@router.post("/membership/order/callback", response_model=None, description="支付回调")
async def order_callback(
    request: Request,
    code: str = Form(...),
    timestamp: str = Form(...),
    mch_id: str = Form(...),
    order_no: str = Form(...),
    out_trade_no: str = Form(...),
    pay_no: str = Form(...),
    total_fee: str = Form(...),
    sign: str = Form(...),
    pay_channel: str = Form(...),
    trade_type: str = Form(...),
    success_time: str = Form(...),
    attach: Optional[str] = Form(None),
    openid: Optional[str] = Form(None),
):
    """
    支付回调 - 处理支付平台的回调通知
    返回要求：
    - 成功：HTTP 200 + 'SUCCESS'（大写）
    - 失败：HTTP 200 + 'FAIL'
    """
    try:
        # logger.info(f"Received callback for order: {order_no}, out_trade_no: {out_trade_no}")
        # 校验签名合法性
        if code == "1":
            return PlainTextResponse("FAIL", status_code=200)

        await MemberOrderCRUD.order_call_back(out_trade_no, order_no)
        return PlainTextResponse("SUCCESS", status_code=200)
    except Exception as e:
        logger.error(f"Error in callback: {str(e)}")
        return PlainTextResponse("FAIL", status_code=200)


# 获取会员计划列表
@router.get(
    "/membership/plans",
    response_model=BaseOut,
    description="获取会员计划列表",
    dependencies=[Depends(verify_user_login)],
)
async def get_plans():
    """
    获取会员计划列表
    """
    plans = await MembershipPlanCRUD.get_plans()
    return resp_200(
        data=[MembershipPlanInfo.model_validate(plan, strict=False) for plan in plans]
    )


# 获取订阅历史
@router.get(
    "/membership/orders",
    response_model=BaseOut,
    description="获取订阅历史",
    dependencies=[Depends(verify_user_login)],
)
async def get_orders(
    page: int = 1, page_size: int = 20, token=Depends(verify_user_login)
):
    """
    获取订阅历史
    """
    orders, total = await MemberOrderCRUD.get_order_list(page, page_size, token.uid)
    return resp_200(
        data=[
            MemberOrderWithPlan.model_validate(order, strict=False) for order in orders
        ],
        total=total,
        page=page,
        page_size=page_size,
    )
