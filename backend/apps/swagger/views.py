#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : swagger_views.py
from datetime import timedelta

from fastapi import APIRouter, Depends, HTTPException
from fastapi.security import OAuth2PasswordRequestForm
from starlette.status import HTTP_401_UNAUTHORIZED

from conf import settings
from core.base_schemas import Token
from core.security import authenticate_user, create_access_token
import uuid

router = APIRouter()


@router.post(
    "/swagger/login/",
    response_model=Token,
    tags=["swagger"],
    description="文档登录接口",
    include_in_schema=False,
)
async def swagger_login(
    user: OAuth2PasswordRequestForm = Depends(),
):
    flag, auth_user = await authenticate_user(user.username, user.password)
    if not flag:
        raise HTTPException(
            status_code=HTTP_401_UNAUTHORIZED,
            detail="Invalid oauth",
        )
    token = str(uuid.uuid4())
    auth_user.token = token
    await auth_user.save()
    access_token_expires = timedelta(hours=settings.ACCESS_TOKEN_EXPIRE_HOURS)
    access_token_expires = timedelta(hours=settings.ACCESS_TOKEN_EXPIRE_HOURS)
    access_token = create_access_token(
        data={
            "email": user.username,
            "uid": auth_user.id,
            "role": auth_user.role,
            "token": token,
        },
        expires_delta=access_token_expires,
    )
    return {
        "access_token": access_token,
        "token_type": "Bearer",
        "email": auth_user.email,
        "uid": auth_user.id,
    }
