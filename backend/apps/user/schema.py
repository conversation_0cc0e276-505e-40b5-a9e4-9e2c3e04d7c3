#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : schema.py
from typing import List, Optional, Union

from fastapi import Query
from pydantic import BaseModel, Field, field_validator
from tortoise.contrib.pydantic import pydantic_model_creator

from core.base_schemas import BaseListOut, ValidatorTime, BaseOut, Token
from models.base import User


class UserLoginRequest(BaseModel):
    email: str = Field(description="邮箱")
    password: str = Field(description="密码")


class UserRegisterRequest(BaseModel):
    invite_code: str | None = Field(description="邀请码", default=None)
    email: str = Field(description="邮箱")
    password: str = Field(description="密码")
    nickname: str | None = Field(description="昵称", default=None)
    machine_code: str | None = Field(description="机器码", default=None)


UserInDetail = pydantic_model_creator(User, name="UserInDb", exclude=("password",))


class UserDetail(UserInDetail, ValidatorTime):
    @field_validator("last_login", check_fields=False)
    @classmethod
    def format_last_login(cls, value) -> str:
        if not value:
            return ""
        if isinstance(value, str):
            return value
        return value.strftime("%Y-%m-%d %H:%M:%S")

    @field_validator("member_expired_time", check_fields=False)
    @classmethod
    def format_member_expired_time(cls, value) -> str:
        if not value:
            return ""
        if isinstance(value, str):
            return value
        return value.strftime("%Y-%m-%d %H:%M:%S")

    @field_validator("member_created_time", check_fields=False)
    @classmethod
    def format_member_created_time(cls, value) -> str:
        if not value:
            return ""
        if isinstance(value, str):
            return value
        return value.strftime("%Y-%m-%d %H:%M:%S")


class UserSimpleDetail(BaseModel):
    id: int
    email: str
    role: str


class UserSimpleList(BaseOut):
    data: List[UserSimpleDetail]


class UserInfo(BaseOut):
    data: UserDetail


class UserList(BaseListOut):
    data: List[UserDetail]


class UserRequestResetPassword(BaseModel):
    user_id: int = Field(description="用户id")
    new_password: str = Field(description="新密码")


UserCreate = pydantic_model_creator(
    User, name="UserCreate", exclude=("id", "create_time", "update_time", "last_login")
)


class UserUpdate(BaseModel):
    email: str = Field(description="邮箱")
    is_active: bool = Field(description="是否激活")
    is_member: bool = Field(description="是否会员")
    member_expired_time: str = Field(description="会员过期时间")
    invite_code: str = Field(description="邀请码")
    has_used_trial: bool = Field(description="是否使用试用")


class UserToken(BaseOut):
    data: Token
