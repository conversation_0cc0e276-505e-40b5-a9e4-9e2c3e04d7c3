#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : views.py
from fastapi import APIRouter, Depends, Query

from core.base_response import resp_200
from core.base_schemas import BaseOut, Token
from core.security import verify_admin_user, verify_user_login
from common.utils import get_shanghai_current_time

from .curd import AuthenticationCRUD, UserModel
from .schema import (
    UserDetail,
    UserLoginRequest,
    UserRequestResetPassword,
    UserUpdate,
    UserInfo,
    UserToken,
    UserRegisterRequest,
)
from common.globals_enums import RoleEnum

router = APIRouter()


@router.post("/auth/login", response_model=UserToken, description="登录")
async def user_login(user: UserLoginRequest):
    """
    登录
    """
    token = await AuthenticationCRUD.login(user, RoleEnum.NORMAL)
    return resp_200(data=token)


@router.post("/auth/register", response_model=BaseOut, description="注册")
async def user_register(user: UserRegisterRequest):
    """
    注册
    """
    await AuthenticationCRUD.register(user)
    return resp_200()


@router.put(
    "/users/reset_password",
    response_model=BaseOut,
    description="修改密码",
    dependencies=[Depends(verify_admin_user)],
)
async def user_reset_password(user_req: UserRequestResetPassword):
    await UserModel.reset_user_password(user_req.user_id, user_req.new_password)
    return resp_200()


@router.put(
    "/users/{user_id}",
    response_model=BaseOut,
    description="修改",
    dependencies=[Depends(verify_admin_user)],
)
async def user_update(user_id, user_obj: UserUpdate):
    await UserModel.update_user(user_id, user_obj)
    return resp_200()


@router.get("/users/info", response_model=UserInfo, description="用户信息")
async def user_info(token=Depends(verify_user_login)):
    uid = token.uid
    user = await UserModel.get_user_by_id(uid)
    # 更新登录时间
    user.last_login = get_shanghai_current_time()
    await user.save()
    return resp_200(data=UserDetail.model_validate(user))
