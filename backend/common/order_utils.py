#!/usr/bin/env python
# -*- coding: utf-8 -*-
import random
import string
import time
import uuid
from datetime import datetime


class OrderNumberGenerator:
    """订单号生成器"""

    @staticmethod
    def generate_order_no(user_id: int, prefix: str = "MO") -> str:
        """
        生成订单号
        格式：MO + 年月日时分秒 + 用户ID后4位 + 4位随机数
        示例：MO202403151234561234RAND
        """
        # 获取当前时间戳
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")

        # 用户ID后4位，不足4位补0
        user_suffix = str(user_id).zfill(4)[-4:]

        # 生成4位随机数
        random_suffix = "".join(random.choices(string.digits, k=4))

        # 组合订单号
        order_no = f"{prefix}{timestamp}{user_suffix}{random_suffix}"

        return order_no
