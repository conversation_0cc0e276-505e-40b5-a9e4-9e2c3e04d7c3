#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : settings.py
from functools import lru_cache

from pydantic import Field
from pathlib import Path

from pydantic_settings import BaseSettings, SettingsConfigDict

BASE_DIR = Path(__file__).resolve().parent.parent
MEDIA_IDR = BASE_DIR / "media"
if not MEDIA_IDR.exists():
    MEDIA_IDR.mkdir(parents=True, exist_ok=True)


class Settings(BaseSettings):
    """
    配置
    """

    DEBUG: bool = Field(default=True, description="是否为开发模式")
    SERVER_NAME: str = Field(default="server", description="项目名")
    # swagger config
    TITLE: str = Field(default="Docs", description="swagger 文档标题")
    DESCRIPTION: str = Field(default="swagger docs ", description="swagger 文档 描述")
    DOCS_URL: str = Field(default="/api/swagger/docs", description="swagger 文档url")
    OPENAPI_URL: str = Field(
        default="/api/swagger/openapi.json", description="swagger OPENAPI_URL"
    )
    REDOC_URL: str = Field(
        default="/api/swagger/redoc", description="swagger REDOC_URL"
    )

    BASE_DIR: Path = Field(default=BASE_DIR, description="项目基础路径")
    # jwt openssl rand -hex 32
    SECRET_KEY: str = Field(
        default="d7b6be3f72015d87a51c24333cef069cdb370946101d019f062a50247f4b63d8",
        description="秘钥值",
    )
    ALGORITHM: str = Field(default="HS256", description="加密算法")
    ACCESS_TOKEN_EXPIRE_HOURS: int = Field(default=24 * 15, description="token 过期时间")

    VERSIONS: str = Field(default="0.0.1", description="版本号")

    # 不需要进行登录验证的url
    # swagger 界面接口测试
    EXCLUDE_API_URL: tuple = (
        "/api/v1/auth/login",
        "/api/v1/auth/register",
        "/api/v1/swagger/",
        "/api/v1/admin/auth/login",
        "/api/v1/membership/order/callback",
        "/",
    )
    EXCLUDE_API_RE_URL: tuple = (
        r"^.*/swagger/.*$",
        r"^.*/media/.*$",
    )
    MEDIA_IDR: Path = MEDIA_IDR
    MEDIA_URL_PREFIX: str = Field(default="/media", description="媒体资源url前缀")
    # 日志
    LOG: dict = {"max_log_size": "20MB", "backup_count": 20}

    DATA_SET_ROOT_PATH: Path = Field(default=str(MEDIA_IDR), description="路径")

    model_config = SettingsConfigDict(env_file=".env")


@lru_cache
def get_settings():
    return Settings()


settings = get_settings()
