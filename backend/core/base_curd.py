#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : base_curd.py
from typing import TypeVar, Union

from fastapi.encoders import jsonable_encoder
from pydantic import BaseModel
from tortoise.models import Model
from tortoise.queryset import QuerySet
from tortoise.exceptions import DoesNotExist, MultipleObjectsReturned

ModelType = TypeVar("ModelType", bound=Model)
CreateSchemaType = TypeVar("CreateSchemaType", bound=BaseModel)
UpdateSchemaType = TypeVar("UpdateSchemaType", bound=BaseModel)


class BaseCURD:
    model: Union[ModelType, None] = None

    @classmethod
    async def create(cls, obj_in: CreateSchemaType):
        obj_in_data = jsonable_encoder(obj_in)
        return await cls.model.create(**obj_in_data)

    @classmethod
    async def update(cls, obj_id: int, obj_in: UpdateSchemaType):
        obj_in_data = jsonable_encoder(obj_in)
        if "id" in obj_in_data:
            del obj_in_data["id"]
        await cls.model.filter(id=obj_id).update(**obj_in_data)

    @classmethod
    async def delete(cls, obj_id: int):
        await cls.model.filter(id=obj_id).delete()

    @classmethod
    async def all(cls):
        return await cls.model.all()

    @classmethod
    async def get_obj_by_id(cls, obj_id: int):
        try:
            obj = await cls.model.get(id=obj_id)
        except (DoesNotExist, MultipleObjectsReturned):
            return None
        return obj

    @classmethod
    async def get_list(cls, page: int, page_size: int = 20) -> tuple[QuerySet, int]:
        offset = (page - 1) * page_size
        query_set = cls.model.all()
        total = await query_set.count()
        data_list = await query_set.offset(offset).limit(page_size).order_by("-id")
        return data_list, total
