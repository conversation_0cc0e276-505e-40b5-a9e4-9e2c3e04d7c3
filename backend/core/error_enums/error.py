#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : error.py

from .exception import ServerException

ParamError = ServerException(10000, "参数错误", "param error")

ObjectNotExist = ServerException(10001, "不存在", "not exist")
UserPasswordError = ServerException(10002, "用户名或密码错误", "email or password error")
UserHasExist = ServerException(10003, "用户已存在", "user has already exist")
OldPasswordError = ServerException(10004, "旧密码错误", "old password error")
PasswordError = ServerException(10005, "密码错误", "password error")
TrialAlreadyUsed = ServerException(10006, "试用已使用", "trial already used")
PaymentError = ServerException(10007, "支付失败", "payment error")
PaymentQRCodeError = ServerException(10008, "生成支付二维码失败", "payment qrcode error")
MachineCodeLimit = ServerException(10009, "注册次数已达上限", "machine code limit")
