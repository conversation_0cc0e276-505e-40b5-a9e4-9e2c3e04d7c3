#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : events.py
from contextlib import asynccontextmanager
from fastapi import FastAPI
from tortoise import Tortoise
from tortoise.contrib.fastapi import RegisterTortoise
from common.log import logger
from db.database import TORTOISE_ORM
from core.scheduler import init_scheduler


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    FastAPI lifespan context manager for startup and shutdown events
    """
    try:
        # Startup
        logger.info("server 正在启动")
        logger.info("正在初始化数据库连接...")
        async with RegisterTortoise(app, config=TORTOISE_ORM):
            logger.info("数据库连接初始化完成")
            # Initialize the scheduler
            init_scheduler()
            logger.info("定时任务初始化完成")
            yield
    except Exception as e:
        logger.error(f"数据库连接初始化失败: {e}")
        raise e
    finally:
        # Shutdown
        logger.info("server正在关闭")
        await Tortoise.close_connections()
