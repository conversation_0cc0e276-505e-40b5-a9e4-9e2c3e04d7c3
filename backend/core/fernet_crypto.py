from cryptography.fernet import Fernet
from typing import Optional
import base64


class FernetCrypto:
    """
    使用 Fernet 实现的对称加密类
    基于 AES-128-CBC 的加密方案，使用固定密钥
    """

    # 固定的 Fernet 密钥 (32 字节的 base64 编码字符串)
    # 在实际应用中，建议将密钥存储在环境变量或配置文件中
    FIXED_KEY = b"ul6Dgvrt4RPLciao3eoiOj6Y_Z2hSYB8coO0hd96MBs="  # 请替换为你的固定密钥

    def __init__(self):
        """
        初始化 Fernet 加密器
        """
        try:
            self.fernet = Fernet(self.FIXED_KEY)
        except Exception as e:
            raise ValueError(f"初始化 Fernet 失败: {str(e)}")

    def encrypt(self, data: str) -> Optional[str]:
        """
        加密字符串数据

        Args:
            data: 需要加密的字符串

        Returns:
            加密后的 base64 编码字符串

        Raises:
            ValueError: 当输入数据无效或加密失败时
        """
        try:
            if not data:
                raise ValueError("加密数据不能为空")

            # 加密数据
            encrypted_data = self.fernet.encrypt(data.encode("utf-8"))
            # 将加密后的字节转换为 base64 字符串
            return base64.b64encode(encrypted_data).decode("utf-8")
        except Exception as e:
            raise ValueError(f"加密失败: {str(e)}")

    def decrypt(self, encrypted_data: str) -> Optional[str]:
        """
        解密加密后的数据

        Args:
            encrypted_data: 加密后的 base64 编码字符串

        Returns:
            解密后的原始字符串

        Raises:
            ValueError: 当输入数据无效或解密失败时
        """
        try:
            if not encrypted_data:
                raise ValueError("解密数据不能为空")

            # 将 base64 字符串转换回字节
            encrypted_bytes = base64.b64decode(encrypted_data.encode("utf-8"))
            # 解密数据
            decrypted_data = self.fernet.decrypt(encrypted_bytes)
            return decrypted_data.decode("utf-8")
        except Exception as e:
            raise ValueError(f"解密失败: {str(e)}")


fernet_crypto = FernetCrypto()


def main():
    """
    示例使用
    """
    try:
        # 测试数据
        original_text = "Hello, 这是一个测试消息！"
        print(f"\n原始数据: {original_text}")

        # 加密
        encrypted_data = fernet_crypto.encrypt(original_text)
        print(f"加密后数据: {encrypted_data}")

        # 解密
        decrypted_data = fernet_crypto.decrypt(encrypted_data)
        print(f"解密后数据: {decrypted_data}")

        # 验证
        assert original_text == decrypted_data, "加密解密验证失败！"
        print("加密解密验证成功！")

    except Exception as e:
        print(f"错误: {str(e)}")


if __name__ == "__main__":
    main()
