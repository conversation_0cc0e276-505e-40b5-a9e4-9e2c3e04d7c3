#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : router.py

"""
register all router like django
Examples:
    import  my_api_router
    router = APIRouter()
    router.include_router(my_api_router)
"""
from fastapi import APIRouter, Depends

from apps.swagger.views import router as swagger_router
from apps.user.views import router as user_router
from core.security import swagger_oauth2_scheme
from apps.membership.views import router as membership_router
from apps.membership.admin import router as membership_admin_router
from apps.user.admin import router as user_admin_router
from apps.statistics.admin import router as statistics_admin_router

api_root_router = APIRouter(
    dependencies=[
        Depends(swagger_oauth2_scheme),
    ]
)
api_v1_router = APIRouter(prefix="/api/v1")

api_v1_router.include_router(user_router, tags=["user"])
api_v1_router.include_router(swagger_router, tags=["swagger"])
api_v1_router.include_router(membership_router, tags=["membership"])

# 管理员
api_v1_router.include_router(membership_admin_router)
api_v1_router.include_router(user_admin_router)
api_v1_router.include_router(statistics_admin_router)


api_root_router.include_router(api_v1_router)


@api_root_router.get("/", description="项目说明")
async def index():
    """
    说明
    """
    return "访问/api/swagger/docs/ 查看接口文档"
