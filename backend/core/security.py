#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : security.py

from datetime import datetime, timedelta, UTC
from typing import Optional, Union, <PERSON>ple
import bcrypt
from fastapi import HTT<PERSON>Exception
from fastapi.security import <PERSON>A<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>earer
from fastapi.security.utils import get_authorization_scheme_param
from jose import JWTError, jwt
from starlette.requests import Request
from starlette.status import HTTP_401_UNAUTHORIZED

from tortoise.exceptions import DoesNotExist, MultipleObjectsReturned
from tortoise.queryset import QuerySetSingle


from models.base import User

from common.globals_enums import RoleEnum
from common.utils import check_re_match_url, get_shanghai_current_time
from conf import settings

from .base_schemas import TokenData
from .error_enums.exception import AuthHTTPException, FORBIDDENHTTPException
from .fernet_crypto import fernet_crypto


def verify_password(plain_password, hashed_password) -> bool:
    """
    Verify the password
    :param plain_password:
    :param hashed_password:
    :return:
    """
    return bcrypt.checkpw(
        plain_password.encode("utf-8"), hashed_password.encode("utf-8")
    )


def get_password_hash(password) -> str:
    """
    Get hashed password
    :param password:
    :return:
    """
    salt = bcrypt.gensalt()
    return bcrypt.hashpw(password.encode("utf-8"), salt).decode("utf-8")


def create_access_token(data: dict, expires_delta: Union[timedelta, None] = None):
    """
    创建jwt token
    :param data:
    :param expires_delta:
    :return:
    """
    to_encode = data.copy()
    if expires_delta:
        expire = get_shanghai_current_time() + expires_delta
    else:
        expire = get_shanghai_current_time() + timedelta(days=7)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(
        to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM
    )
    encrypted_token = fernet_crypto.encrypt(encoded_jwt)
    return encrypted_token


def parse_access_token(token: str) -> TokenData:
    """
    解析token
    :param token:
    :return:
    """
    try:
        decrypted_token = fernet_crypto.decrypt(token)
        payload = jwt.decode(
            decrypted_token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM]
        )
        email: str = payload.get("email")
        if email is None:
            raise AuthHTTPException()
        token_data = TokenData(**payload)
    except (JWTError, ValueError):
        raise AuthHTTPException()
    return token_data


async def authenticate_user(
    email: str, password: str
) -> Tuple[bool, Union[bool, QuerySetSingle]]:
    """
    校验用户登录
    :param email:
    :param password:
    :return:
    """
    try:
        user = await User.get(email=email)
    except (DoesNotExist, MultipleObjectsReturned):
        return False, None
    if not verify_password(password, user.password):
        return False, None
    return True, user


class VerifyOAuthUserBearer:
    def __init__(self, role: list = []):
        self.role = role

    async def __call__(self, request: Request) -> Union[dict, TokenData]:
        authorization = request.headers.get("Authorization")
        scheme, param = get_authorization_scheme_param(authorization)
        if not authorization or scheme.lower() != "bearer":
            raise AuthHTTPException()
        token = parse_access_token(param)
        try:
            await User.get(token=token.token)
        except DoesNotExist:
            raise AuthHTTPException()
        if self.role:
            if token.role not in self.role:
                raise FORBIDDENHTTPException()
        return token


# 验证登录的用户
verify_user_login = VerifyOAuthUserBearer()

# 验证必须是admin用户
verify_admin_user = VerifyOAuthUserBearer(role=[RoleEnum.ADMIN])


class SwaggerOAuth2PasswordBearer(OAuth2PasswordBearer):
    async def __call__(self, request: Request) -> Optional[str]:
        url_path = request.url.path
        print(url_path)
        if url_path in settings.EXCLUDE_API_URL or check_re_match_url(
            url_path, settings.EXCLUDE_API_RE_URL
        ):
            return
        authorization = request.headers.get("Authorization")
        scheme, token = get_authorization_scheme_param(authorization)
        if not authorization or scheme.lower() != "bearer":
            if self.auto_error:
                raise HTTPException(
                    status_code=HTTP_401_UNAUTHORIZED,
                    detail="Not authenticated",
                    headers={"WWW-Authenticate": "Bearer"},
                )
            else:
                return None
        return token


swagger_oauth2_scheme = SwaggerOAuth2PasswordBearer(tokenUrl="/api/v1/swagger/login/")
