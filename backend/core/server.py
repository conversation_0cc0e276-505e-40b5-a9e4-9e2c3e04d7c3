#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : server.py

from fastapi.exceptions import RequestValidationError
from fastapi.middleware.cors import CORSMiddleware
from starlette.exceptions import HTTPException
from starlette.staticfiles import StaticFiles

from common.log import logger
from conf import settings
from core import events, exception_handlers
from core.error_enums.exception import ServerException
from core.router import api_root_router

from fastapi.openapi.docs import (
    get_redoc_html,
    get_swagger_ui_html,
    get_swagger_ui_oauth2_redirect_html,
)

from fastapi import FastAPI


def create_app() -> FastAPI:
    """
    create app obj
    :return: FastAPI
    """
    app = FastAPI(
        debug=settings.DEBUG,
        title=settings.TITLE,
        openapi_url=settings.OPENAPI_URL,
        description=settings.DESCRIPTION,
        docs_url=None,
        redoc_url=None,
        lifespan=events.lifespan,
        openapi_tags=[
            {"name": "user", "description": "用户相关接口"},
        ],
    )
    # 日志文件
    logger.setup(settings.SERVER_NAME)

    # register router
    register_router(app)
    register_middle(app)
    # 挂载静态文件
    app.mount(
        settings.MEDIA_URL_PREFIX,
        StaticFiles(directory=settings.MEDIA_IDR),
        name="media",
    )
    # 注册自定义异常
    register_handler_exception(app)

    if settings.DEBUG:
        register_swagger(app)

    return app


def register_router(app: FastAPI) -> None:
    """
    register router
    :param app:
    :return:
    """
    app.include_router(
        api_root_router,
    )


def register_middle(app: FastAPI) -> None:
    """
    register middle
    :param app:
    :return:
    """
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # 这里可以设置允许的源，例如："http://localhost:8080"
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )


def register_handler_exception(app: FastAPI) -> None:
    """
    注册异常
    :param app:
    :return:
    """
    app.add_exception_handler(Exception, exception_handlers.system_error_handler)
    app.add_exception_handler(HTTPException, exception_handlers.http_error_handler)
    app.add_exception_handler(
        ServerException, exception_handlers.custom_exception_handler
    )
    app.add_exception_handler(
        RequestValidationError, exception_handlers.validate_error_handler
    )


def register_swagger(app: FastAPI) -> None:
    """
    注册swagger
    :param app:
    :return:
    """

    @app.get(f"{settings.DOCS_URL}", include_in_schema=False)
    async def custom_swagger_ui_html():
        return get_swagger_ui_html(
            openapi_url=app.openapi_url,
            title=app.title + " - Swagger UI",
            oauth2_redirect_url=app.swagger_ui_oauth2_redirect_url,
            swagger_js_url="https://cdn.bootcdn.net/ajax/libs/swagger-ui/5.20.2/swagger-ui-bundle.js",
            swagger_css_url="https://cdn.bootcdn.net/ajax/libs/swagger-ui/5.20.2/swagger-ui.css",
        )

    @app.get(app.swagger_ui_oauth2_redirect_url, include_in_schema=False)
    async def swagger_ui_redirect():
        return get_swagger_ui_oauth2_redirect_html()

    @app.get(f"{settings.REDOC_URL}", include_in_schema=False)
    async def redoc_html():
        return get_redoc_html(
            openapi_url=app.openapi_url,
            title=app.title + " - ReDoc",
            redoc_js_url="https://unpkg.com/redoc@next/bundles/redoc.standalone.js",
        )
