#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : database.py
from tortoise import Tortoise

from conf.settings import settings

"""
DATABASE : support sqlite+aiosqlite, mysql+asyncmy, postgresql+asyncpg
mysql：
DATABASE = {
        "DB_TYPE": "mysql",
        "HOST":"127.0.0.1",
        "NAME": "db_name",
        "PASSWORD":"db_password",
        "PORT":"3306",
        "USER": "db_user"
}
"""
# DATABASE = {
#     "DB_TYPE": "sqlite",
#     "NAME": BASE_DIR
#             / (os.getenv("TEST_DATABASE") if os.getenv("TEST_DATABASE") else "db.sqlite3"),
# }
# docker run --rm -d --name demo-mysql -e MYSQL_ROOT_PASSWORD=docker -e MYSQL_DATABASE=test -p 3307:3306 mysql:8.2.0
if settings.DEBUG:
    DATABASE = {
        "DB_TYPE": "mysql",
        "HOST": "127.0.0.1",
        "NAME": "test",
        "PASSWORD": "docker",
        "PORT": "3307",
        "USER": "root",
    }
# 生产环境 创建数据库 lxt_tools：
# CREATE DATABASE lxt_tools CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
else:
    DATABASE = {
        "DB_TYPE": "mysql",
        "HOST": "127.0.0.1",
        "NAME": "lxt_tools",
        "PASSWORD": "NT28xrUP4fC2V67EUnYY",
        "PORT": "3306",
        "USER": "root",
    }


def get_db_url() -> str:
    """
    获取数据库连接的url
    https://tortoise.github.io/databases.html?h=wal#tortoise.Tortoise.generate_schemas-parameters
    :return:
    """
    _data_base = DATABASE
    _db_type = _data_base.get("DB_TYPE")
    if _db_type == "sqlite":
        sqlite_name = _data_base["NAME"]
        url = f"sqlite://{sqlite_name}"
    elif _db_type == "mysql":
        url = f'mysql://{_data_base["USER"]}:{_data_base["PASSWORD"]}@{_data_base["HOST"]}:{_data_base["PORT"]}/{_data_base["NAME"]}'
    elif _db_type == "postgresql":
        url = f'postgres://{_data_base["USER"]}:{_data_base["PASSWORD"]}@{_data_base["HOST"]}:{_data_base["PORT"]}/{_data_base["NAME"]}'
    else:
        raise Exception(f"Not support the {_db_type}")
    return url


# 包含所有的模型，比如生成的视图不需要迁移
TORTOISE_ORM = {
    "connections": {"default": get_db_url()},
    "apps": {
        "models": {
            "models": ["models.base", "aerich.models"],
            "default_connection": "default",
        },
    },
    "use_tz": False,
    "timezone": "Asia/Shanghai",
}


# 只包含需要迁移的模型
TORTOISE_ORM_MIGRATE = {
    "connections": {"default": get_db_url()},
    "apps": {
        "models": {
            "models": ["models.base", "aerich.models"],
            "default_connection": "default",
        },
    },
    "use_tz": False,
    "timezone": "Asia/Shanghai",
}


async def do_stuff():
    """
    当需要单独脚本使用orm时，需要执行这个初始化
    :return:
    """
    await Tortoise.init(config=TORTOISE_ORM)
