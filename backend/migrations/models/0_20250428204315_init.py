from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS `membership_plan` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `create_time` DATETIME(6) NOT NULL COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP(6),
    `update_time` DATETIME(6) NOT NULL COMMENT '更新时间' DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `name` VARCHAR(64) NOT NULL UNIQUE COMMENT '会员计划名称',
    `duration_days` INT NOT NULL COMMENT '会员时长(天)',
    `original_price` DECIMAL(10,2) NOT NULL COMMENT '原价',
    `discount_price` DECIMAL(10,2) NOT NULL COMMENT '折扣价',
    `description` LONGTEXT COMMENT '描述',
    `is_active` BOOL NOT NULL COMMENT '是否启用' DEFAULT 1
) CHARACTER SET utf8mb4 COMMENT='会员计划';
CREATE TABLE IF NOT EXISTS `user` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `create_time` DATETIME(6) NOT NULL COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP(6),
    `update_time` DATETIME(6) NOT NULL COMMENT '更新时间' DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `nickname` VARCHAR(32) COMMENT '账户名',
    `email` VARCHAR(64) NOT NULL COMMENT '邮箱',
    `platform` VARCHAR(64) NOT NULL COMMENT '平台' DEFAULT 'liangxiangtools',
    `password` VARCHAR(128) NOT NULL COMMENT '密码',
    `last_login` DATETIME(6) COMMENT '最近登录时间',
    `is_active` BOOL NOT NULL COMMENT '状态' DEFAULT 1,
    `has_used_trial` BOOL NOT NULL COMMENT '是否已使用过试用期' DEFAULT 0,
    `role` VARCHAR(1) NOT NULL COMMENT '角色 ADMIN--1 NORMAL--2' DEFAULT '2',
    `invite_code` VARCHAR(32) UNIQUE COMMENT '用户的邀请码',
    `is_member` BOOL NOT NULL COMMENT '是否是会员' DEFAULT 0,
    `member_expired_time` DATETIME(6) COMMENT '会员过期时间',
    `member_created_time` DATETIME(6) COMMENT '会员创建时间',
    `total_member_days` INT NOT NULL COMMENT '累计会员天数' DEFAULT 0,
    `token` VARCHAR(128) COMMENT 'token',
    `machine_code` VARCHAR(128) COMMENT '机器码',
    `invited_by_id` BIGINT COMMENT '邀请人',
    UNIQUE KEY `uid_user_email_7dfd53` (`email`, `platform`),
    CONSTRAINT `fk_user_user_ca947b1d` FOREIGN KEY (`invited_by_id`) REFERENCES `user` (`id`) ON DELETE CASCADE,
    KEY `idx_user_email_1b4f1c` (`email`)
) CHARACTER SET utf8mb4 COMMENT='用户';
CREATE TABLE IF NOT EXISTS `invite_record` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `create_time` DATETIME(6) NOT NULL COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP(6),
    `update_time` DATETIME(6) NOT NULL COMMENT '更新时间' DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `reward_claimed` BOOL NOT NULL COMMENT '是否已领取奖励' DEFAULT 0,
    `reward_time` DATETIME(6) COMMENT '奖励发放时间',
    `invitee_id` BIGINT NOT NULL COMMENT '被邀请人',
    `inviter_id` BIGINT NOT NULL COMMENT '邀请人',
    UNIQUE KEY `uid_invite_reco_inviter_e53e05` (`inviter_id`, `invitee_id`),
    CONSTRAINT `fk_invite_r_user_8aaf30db` FOREIGN KEY (`invitee_id`) REFERENCES `user` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_invite_r_user_764973e0` FOREIGN KEY (`inviter_id`) REFERENCES `user` (`id`) ON DELETE CASCADE
) CHARACTER SET utf8mb4 COMMENT='邀请记录';
CREATE TABLE IF NOT EXISTS `member_order` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `create_time` DATETIME(6) NOT NULL COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP(6),
    `update_time` DATETIME(6) NOT NULL COMMENT '更新时间' DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `order_no` VARCHAR(64) NOT NULL UNIQUE COMMENT '订单号',
    `original_amount` DECIMAL(10,2) NOT NULL COMMENT '原价金额',
    `discount_amount` DECIMAL(10,2) NOT NULL COMMENT '折扣金额',
    `actual_amount` DECIMAL(10,2) NOT NULL COMMENT '实际支付金额',
    `status` VARCHAR(9) NOT NULL COMMENT '订单状态' DEFAULT 'pending',
    `pay_time` DATETIME(6) COMMENT '支付时间',
    `pay_channel` VARCHAR(32) COMMENT '支付渠道',
    `transaction_id` VARCHAR(128) COMMENT '交易流水号',
    `plan_id` BIGINT NOT NULL COMMENT '会员计划',
    `user_id` BIGINT NOT NULL COMMENT '用户',
    CONSTRAINT `fk_member_o_membersh_da991dea` FOREIGN KEY (`plan_id`) REFERENCES `membership_plan` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_member_o_user_756c89ed` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE
) CHARACTER SET utf8mb4 COMMENT='会员订单';
CREATE TABLE IF NOT EXISTS `aerich` (
    `id` INT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `version` VARCHAR(255) NOT NULL,
    `app` VARCHAR(100) NOT NULL,
    `content` JSON NOT NULL
) CHARACTER SET utf8mb4;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        """
