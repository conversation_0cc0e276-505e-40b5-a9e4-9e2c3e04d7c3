#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : model.py
from datetime import datetime
from typing import Union

from tortoise import Model, fields

from common.globals_enums import (
    RoleEnum,
    OrderStatusEnum,
)


class BaseCreateModel(Model):
    id = fields.BigIntField(pk=True)
    create_time: Union[str, datetime] = fields.DatetimeField(
        auto_now_add=True, description="创建时间"
    )

    class Meta:
        abstract = True


class BaseUpsertModel(BaseCreateModel):
    update_time: Union[str, datetime] = fields.DatetimeField(
        auto_now=True, description="更新时间"
    )

    class Meta:
        abstract = True


class User(BaseUpsertModel):
    nickname = fields.CharField(max_length=32, description="账户名", null=True, blank=True)
    email = fields.CharField(max_length=64, description="邮箱", index=True)
    platform = fields.CharField(
        max_length=64, description="平台", default="liangxiangtools"
    )
    password = fields.CharField(max_length=128, description="密码")
    last_login: Union[str, datetime, None] = fields.DatetimeField(
        null=True, description="最近登录时间"
    )
    is_active = fields.BooleanField(default=True, description="状态")
    has_used_trial = fields.BooleanField(default=False, description="是否已使用过试用期")
    role = fields.CharEnumField(
        RoleEnum,
        default=RoleEnum.NORMAL,
        description=f"角色 {' '.join(['--'.join([str(enum.name), str(enum.value)]) for enum in RoleEnum])}",
    )
    invite_code: Union[str, None] = fields.CharField(
        max_length=32, null=True, unique=True, description="用户的邀请码"
    )
    invited_by = fields.ForeignKeyField(
        "models.User", null=True, related_name="invited_users", description="邀请人"
    )
    is_member = fields.BooleanField(default=False, description="是否是会员")
    member_expired_time: Union[str, datetime, None] = fields.DatetimeField(
        null=True, description="会员过期时间"
    )
    member_created_time: Union[str, datetime, None] = fields.DatetimeField(
        null=True, description="会员创建时间"
    )
    total_member_days = fields.IntField(default=0, description="累计会员天数")
    token = fields.CharField(max_length=128, null=True, description="token")
    # 机器码
    machine_code = fields.CharField(max_length=128, null=True, description="机器码")
    # 是否是付费用户
    is_pay_user = fields.BooleanField(default=False, description="是否是付费用户")

    class Meta:
        table_description = "用户"
        table = "user"
        unique_together = (("email", "platform"),)


class MembershipPlan(BaseUpsertModel):
    name = fields.CharField(max_length=64, description="会员计划名称", unique=True)
    duration_days = fields.IntField(description="会员时长(天)")
    original_price = fields.DecimalField(
        max_digits=10, decimal_places=2, description="原价"
    )
    discount_price = fields.DecimalField(
        max_digits=10, decimal_places=2, description="折扣价"
    )
    description = fields.TextField(null=True, description="描述")
    is_active = fields.BooleanField(default=True, description="是否启用")

    class Meta:
        table_description = "会员计划"
        table = "membership_plan"


class MemberOrder(BaseUpsertModel):
    user = fields.ForeignKeyField(
        "models.User", related_name="orders", description="用户"
    )
    plan = fields.ForeignKeyField(
        "models.MembershipPlan", related_name="orders", description="会员计划"
    )
    order_no = fields.CharField(max_length=64, unique=True, description="订单号")
    original_amount = fields.DecimalField(
        max_digits=10, decimal_places=2, description="原价金额"
    )
    discount_amount = fields.DecimalField(
        max_digits=10, decimal_places=2, description="折扣金额"
    )
    actual_amount = fields.DecimalField(
        max_digits=10, decimal_places=2, description="实际支付金额"
    )
    status = fields.CharEnumField(
        OrderStatusEnum, default=OrderStatusEnum.PENDING, description="订单状态"
    )
    duration_days = fields.IntField(
        default=0, description="会员时长(天)", null=True, blank=True
    )
    pay_time = fields.DatetimeField(null=True, description="支付时间")
    pay_channel = fields.CharField(max_length=32, null=True, description="支付渠道")
    transaction_id = fields.CharField(max_length=128, null=True, description="交易流水号")

    class Meta:
        table_description = "会员订单"
        table = "member_order"


class InviteRecord(BaseUpsertModel):
    inviter = fields.ForeignKeyField(
        "models.User", related_name="sent_invitations", description="邀请人"
    )
    invitee = fields.ForeignKeyField(
        "models.User", related_name="received_invitations", description="被邀请人"
    )
    reward_claimed = fields.BooleanField(default=False, description="是否已领取奖励")
    reward_time = fields.DatetimeField(null=True, description="奖励发放时间")

    class Meta:
        table_description = "邀请记录"
        table = "invite_record"
        unique_together = (("inviter", "invitee"),)
