#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : init_data.py

import asyncio
import sys
from pathlib import Path

from tortoise import Tortoise

sys.path.insert(0, str(Path(__file__).resolve().parent.parent))  # isort:skip

from common.log import logger  # isort:skip
from common.globals_enums import RoleEnum  # isort:skip
from core.security import get_password_hash  # isort:skip
from db.database import do_stuff  # isort:skip
from models.base import User, MembershipPlan  # isort:skip


async def create_admin_account():
    """
    初始化 管理员账号
    :return:
    """
    admin = await User.exists(email="<EMAIL>")
    if not admin:
        await User.create(
            email="<EMAIL>",
            role=RoleEnum.ADMIN,
            password=get_password_hash("<EMAIL>"),
        )


async def initialize_membership_plans():
    """Initialize default membership plans"""
    # Create the free trial plan if it doesn't exist
    await MembershipPlan.get_or_create(
        name="首次激活赠送",
        defaults={
            "duration_days": 5,
            "original_price": 0,
            "discount_price": 0,
            "description": "首次激活赠送",
            "is_active": True,
        },
    )
    await MembershipPlan.get_or_create(
        name="邀请奖励",
        defaults={
            "duration_days": 7,
            "original_price": 0,
            "discount_price": 0,
            "description": "邀请奖励",
            "is_active": True,
        },
    )
    await MembershipPlan.get_or_create(
        name="月卡",
        defaults={
            "duration_days": 30,
            "original_price": 10,
            "discount_price": 7,
            "description": "月卡",
            "is_active": True,
        },
    )
    # 季卡
    await MembershipPlan.get_or_create(
        name="季卡",
        defaults={
            "duration_days": 90,
            "original_price": 30,
            "discount_price": 19,
            "description": "季卡",
            "is_active": True,
        },
    )
    # 年卡
    await MembershipPlan.get_or_create(
        name="年卡",
        defaults={
            "duration_days": 365,
            "original_price": 120,
            "discount_price": 69,
            "description": "年卡",
            "is_active": True,
        },
    )


async def main() -> None:
    logger.info("init database")
    await do_stuff()
    logger.info("init admin account")
    await create_admin_account()
    logger.info("init membership plans")
    await initialize_membership_plans()
    await Tortoise.close_connections()


if __name__ == "__main__":
    asyncio.run(main())
