#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : init_data.py

import asyncio
import sys
from pathlib import Path

from tortoise import Tortoise

sys.path.insert(0, str(Path(__file__).resolve().parent.parent))  # isort:skip

from common.log import logger  # isort:skip
from db.database import do_stuff  # isort:skip
from models.base import User, MembershipPlan, MemberOrder  # isort:skip


async def update_user_is_pay():
    """update user is pay"""
    # Create the free trial plan if it doesn't exist
    users = await User.all()
    for user in users:
        logger.info(f'check {user.email}')
        orders = await MemberOrder.filter(user=user, status="paid", actual_amount__gt=0)
        if orders:
            user.is_pay_user = True
            await user.save()


async def main() -> None:
    logger.info("init database")
    await do_stuff()
    logger.info("update user is pay")
    await update_user_is_pay()
    await Tortoise.close_connections()


if __name__ == "__main__":
    asyncio.run(main())
