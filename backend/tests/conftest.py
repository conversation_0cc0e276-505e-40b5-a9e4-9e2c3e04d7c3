#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : conftest.py
import pytest
from httpx import AsyncClient
from tortoise import Tortoise

from models.base import *
from core.server import create_app
from core.security import get_password_hash

DB_URL = "sqlite://:memory:"
admin_email, admin_password = ["admin", "admin"]
test_email, test_password = ["test_user", "test_password"]

# Configure Tortoise ORM


async def init_db(db_url, create_db: bool = False, schemas: bool = False) -> None:
    """Initial database connection"""

    TORTOISE_ORM = {
        "connections": {"default": db_url},
        "apps": {
            "base": {
                "models": ["models.base"],
                "default_connection": "default",
            },
        },
        "use_tz": False,
        "timezone": "Asia/Shanghai",
    }
    await Tortoise.init(
        config=TORTOISE_ORM,
        _create_db=create_db,
    )
    if create_db:
        print(f"Database created! {db_url}")
    if schemas:
        await Tortoise.generate_schemas()
        print("Success to generate schemas")


async def init(db_url: str = DB_URL):
    await init_db(db_url, True, True)


@pytest.fixture(scope="session")
def anyio_backend():
    return "asyncio"


@pytest.fixture(scope="session")
async def client():
    async with AsyncClient(app=create_app(), base_url="http://test") as client:
        print("Client is ready")
        yield client


@pytest.fixture(scope="session")
async def client_with_admin_token(client: AsyncClient):
    data = {"email": admin_email, "password": admin_password}
    response = await client.post("/api/auth/login", json=data)
    token_type = response.json().get("data").get("token_type")
    access_token = response.json().get("data").get("access_token")
    async with AsyncClient(
        app=create_app(),
        base_url="http://test",
        headers={"Authorization": f"{token_type} {access_token}"},
    ) as admin_client:
        yield admin_client


@pytest.fixture(scope="session")
async def client_with_norm_token(client: AsyncClient):
    """
    客户端
    :param client:
    :return:
    """
    data = {"email": test_email, "password": test_password}
    response = await client.post("/api/auth/login", json=data)
    token_type = response.json().get("data").get("token_type")
    access_token = response.json().get("data").get("access_token")
    async with AsyncClient(
        app=create_app(),
        base_url="http://test",
        headers={"Authorization": f"{token_type} {access_token}"},
    ) as norm_client:
        yield norm_client


async def init_sql():
    # 插入管理员账号
    await User.create(
        email=admin_email,
        password=get_password_hash(admin_password),
        role=RoleEnum.ADMIN,
    )
    await User.create(
        email=test_email,
        password=get_password_hash(test_password),
        role=RoleEnum.NORMAL,
    )


@pytest.fixture(scope="session", autouse=True)
async def initialize_tests():
    """
    初始化
    :return:
    """
    print("init db")
    await init()
    await init_sql()
    yield
    await Tortoise._drop_databases()
    print("cleanup db")
