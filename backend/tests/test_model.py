#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : test_model.py
import json

import pytest
from tortoise.exceptions import DoesNotExist

from common.globals_enums import RoleEnum
from core.security import get_password_hash

from models.base import *


@pytest.mark.anyio
async def test_user_crud():
    # 创建用户
    user = await User.create(
        email="test user",
        password=get_password_hash("password123"),
        role=RoleEnum.NORMAL,
    )

    # 查询用户是否存在
    assert await User.exists(email="test user")

    # 更新用户信息
    await User.filter(email="test user").update(role="1")
    updated_user = await User.get(email="test user")
    assert updated_user.role == "1"

    # 删除用户
    await User.filter(email="test user").delete()

    # 确认用户已被删除
    with pytest.raises(DoesNotExist):
        await User.get(email="test user")
