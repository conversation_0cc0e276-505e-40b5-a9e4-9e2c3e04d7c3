#!/bin/bash

# Database credentials
DB_NAME="lxt_tools"
DB_USER="root"
DB_PASS="NT28xrUP4fC2V67EUnYY"

# Backup directory
BACKUP_DIR="/backup/mysql"
DATE=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILE="${BACKUP_DIR}/${DB_NAME}_${DATE}.sql"

# Make sure backup directory exists
mkdir -p $BACKUP_DIR

# Log file
LOG_FILE="${BACKUP_DIR}/backup_log.txt"

echo "Starting backup of ${DB_NAME} at $(date)" >> $LOG_FILE

# Perform the backup
mysqldump --user=$DB_USER --password=$DB_PASS $DB_NAME > $BACKUP_FILE 2>> $LOG_FILE

# Check if backup was successful
if [ $? -eq 0 ]; then
    echo "Backup completed successfully at $(date)" >> $LOG_FILE
    
    # Compress the backup file
    gzip $BACKUP_FILE
    echo "Backup compressed: ${BACKUP_FILE}.gz" >> $LOG_FILE
    
    # Delete backups older than 15 days
    find $BACKUP_DIR -name "${DB_NAME}_*.sql.gz" -mtime +15 -delete
    echo "Old backups cleaned up" >> $LOG_FILE
else
    echo "Backup failed at $(date)" >> $LOG_FILE
fi

echo "====================" >> $LOG_FILE
