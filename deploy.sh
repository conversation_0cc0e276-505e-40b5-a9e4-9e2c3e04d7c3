#!/bin/bash

# 设置严格模式
set -euo pipefail
IFS=$'\n\t'

# 定义颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
check_command() {
    if ! command -v "$1" &> /dev/null; then
        log_error "$1 命令未找到，请先安装"
        exit 1
    fi
}

# 检查必要的命令
check_required_commands() {
    log_info "检查必要的命令..."
    check_command "supervisorctl"
    check_command "nginx"
    check_command "pnpm"
}

# 检查文件是否存在
check_file() {
    if [ ! -f "$1" ]; then
        log_error "文件 $1 不存在"
        exit 1
    fi
}

# 部署后端
deploy_backend() {
    log_info "开始部署后端..."
    
    # 进入后端目录
    cd backend || exit 1
    
    # 配置环境文件
    if [ ! -f .env ]; then
        log_info "创建 .env 文件..."
        if [ -f example.env ]; then
            cp example.env .env
            log_info ".env 文件已创建"
        else
            log_error "example.env 文件不存在"
            exit 1
        fi
    fi

    # 返回上一级
    cd ..

    # 检查部署文件
    check_file "./deploy/server_supervisor.conf"
    check_file "./deploy/api_nginx.conf"

    # 部署 supervisor 配置
    log_info "配置 supervisor..."
    if [ -f "/etc/supervisor/conf.d/server_supervisor.conf" ]; then
        cp ./deploy/server_supervisor.conf -f /etc/supervisor/conf.d/
    else
        cp ./deploy/server_supervisor.conf /etc/supervisor/conf.d/
    fi

    # 重启 supervisor
    log_info "重启 supervisor 服务..."
    supervisorctl update
    supervisorctl restart all

    # 部署 nginx 配置
    log_info "配置 nginx..."
    if [ -f "/etc/nginx/conf.d/api_nginx.conf" ]; then
        cp ./deploy/api_nginx.conf -f /etc/nginx/conf.d/
    else
        cp ./deploy/api_nginx.conf /etc/nginx/conf.d/
    fi

    # 检查 nginx 配置
    log_info "检查 nginx 配置..."
    nginx -t || {
        log_error "Nginx 配置检查失败"
        exit 1
    }

    # 重启 nginx
    log_info "重启 nginx 服务..."
    nginx -s reload
}

# 部署前端
deploy_frontend() {
    log_info "开始部署前端..."
    
    # 进入前端目录
    cd frontend || exit 1
    
    # 安装依赖
    log_info "安装前端依赖..."
    pnpm install || {
        log_error "前端依赖安装失败"
        exit 1
    }

    # 构建前端
    log_info "构建前端..."
    pnpm build || {
        log_error "前端构建失败"
        exit 1
    }
}

# 主函数
main() {
    log_info "开始部署流程..."
    
    # 检查命令
    check_required_commands
    
    # 记录开始时间
    start_time=$(date +%s)
    
    # 部署后端
    deploy_backend
    
    # 部署前端
    deploy_frontend
    
    # 计算耗时
    end_time=$(date +%s)
    duration=$((end_time - start_time))
    
    log_info "部署完成! 总耗时: ${duration} 秒"
}

# 执行主函数
main "$@"




