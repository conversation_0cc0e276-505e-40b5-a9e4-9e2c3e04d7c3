# 强制 HTTP 跳转 HTTPS
server {
    listen 80;
    server_name 20133075.xyz www.20133075.xyz;

    # certbot 用于 HTTP-01 验证路径（保持原样）
    location ~ ^/.well-known/acme-challenge/ {
        root /var/www/html;
    }

    # 其余请求跳转 HTTPS
    location / {
        return 301 https://$host$request_uri;
    }
}

# 主服务：HTTPS + 前端 + API 代理
server {
    listen 443 ssl http2;
    server_name 20133075.xyz www.20133075.xyz;

    # Certbot 生成的证书路径（保持不变）
    ssl_certificate /etc/letsencrypt/live/20133075.xyz/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/20133075.xyz/privkey.pem;

    # SSL 安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers 'ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256';
    ssl_prefer_server_ciphers on;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    ssl_session_tickets off;

    # STAPLING（可选，但推荐）
    # ssl_stapling on;
    # ssl_stapling_verify on;
    resolver ******* ******* valid=300s;
    resolver_timeout 5s;

    # 安全响应头
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-Content-Type-Options "nosniff";
    add_header X-XSS-Protection "1; mode=block";
    add_header Referrer-Policy "strict-origin-when-cross-origin";
    add_header Permissions-Policy "geolocation=(), microphone=(), camera=()";
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; object-src 'none'; frame-ancestors 'none';" always;

    # Gzip 压缩
    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml+rss text/javascript;
    gzip_min_length 256;

    # 前端静态资源缓存
    location ~* \.(?:js|css|png|jpg|jpeg|gif|ico|svg|woff2?|ttf|eot)$ {
        root /var/www/matting-website/dist;
        expires 1y;
        access_log off;
        add_header Cache-Control "public, immutable";
    }

    # index.html 单独处理避免缓存
    location = /index.html {
        root /var/www/matting-website/dist;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
    }

    # 主静态入口
    location / {
        root /var/www/matting-website/dist;
        index index.html;
        try_files $uri $uri/ /index.html;
    }
    # 禁止访问隐藏文件
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
}
