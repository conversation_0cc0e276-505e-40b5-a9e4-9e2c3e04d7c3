[program:app_server]
command=/root/.pyenv/versions/xb-user/bin/gunicorn -w 4 -b 127.0.0.1:8010 -k uvicorn.workers.UvicornWorker main:app
directory=/root/LXT/XB-user/backend
user=root
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
redirect_stderr=true
stdout_logfile=/var/log/app_server.log
stdout_logfile_maxbytes=100MB
stdout_logfile_backups=10
environment=PATH="/root/.pyenv/versions/xb-user/bin:%(ENV_PATH)s",PYENV_VERSION="xb-user"
