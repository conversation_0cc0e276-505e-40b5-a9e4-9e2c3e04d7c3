{"name": "web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@ant-design/icons-vue": "^6.1.0", "ant-design-vue": "^4.0.2", "axios": "^1.5.0", "echarts": "^5.6.0", "pinia": "^2.1.6", "pinia-plugin-persistedstate": "^3.2.0", "vue": "^3.3.4", "vue-echarts": "^7.0.3", "vue-router": "^4.2.4"}, "devDependencies": {"@vitejs/plugin-vue": "^4.2.3", "sass": "^1.87.0", "unplugin-vue-components": "^0.25.2", "vite": "^4.4.5"}}