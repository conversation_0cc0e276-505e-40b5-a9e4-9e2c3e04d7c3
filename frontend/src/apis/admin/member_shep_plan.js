import httpInstance from "@/utils/http";


/**
 * 
 * @description: 获取数据
 * @params { 
     page: 1,
     page_size: 20,
   } 
 * @return {*}
 */
export const getListAPI = (params) => {
  return httpInstance({
    url: '/api/v1/admin/plans',
    method: 'GET',
    params
  })

}

/**
 * 删除
 * @param {*} id 
 * @returns 
 */
export const delleteAPI = (id) => {
  return httpInstance({
    url: `/api/v1/admin/plans/${id}`,
    method: 'DELETE',

  })
}


/**
 * 更新
 * @param {*} datas 
 * @returns 
 */
export const updateAPI = (datas, id) => {
  return httpInstance({
    url: `/api/v1/admin/plans/${id}`,
    method: "PUT",
    data: datas,
  })
}

export const createAPI = (datas) => {
  return httpInstance({
    url: '/api/v1/admin/plans',
    method: "POST",
    data: datas,
  })
}

/**
 * 获取所有会员计划
 * @returns 
 */
export const getAllPlansAPI = () => {
  return httpInstance({
    url: '/api/v1/admin/allplans',
    method: 'GET',
  })
}

/**
 * 赠送会员时长
 * @param {*} datas 
 * @returns 
 */
export const grantMembershipDurationAPI = (datas) => {
  return httpInstance({
    url: '/api/v1/admin/membership/grant',
    method: 'POST',
    data: datas,
  })
}
