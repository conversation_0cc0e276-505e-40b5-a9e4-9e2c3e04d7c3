import httpInstance from "@/utils/http";


export const loginAPI = ({email, password})=>{
    return httpInstance({
        url:'/api/v1/admin/auth/login',
        method:'POST',
        data:{
            email,
            password
        }
    })
}


/**
 * @description: 获取数据
 * @params { 
     page: 1,
     page_size: 20,
     search: ''
   } 
 * @return {*}
 */
   export const getListAPI = (params) => {
    return httpInstance({
      url: '/api/v1/admin/users',
      method: 'GET',
      params
    })

  }
  
  /**
   * 删除
   * @param {*} id 
   * @returns 
   */
  export const delleteAPI = (id) => {
    return httpInstance({
      url: `/api/v1/admin/users/${id}`,
      method: 'DELETE',
  
    })
  }
  
  
  /**
   * 批量删除
   * @param {*} ids 
   * @returns 
   */
  export const bathDeleteAPI = (ids) => {
    return httpInstance({
      url: '/api/v1/admin/batch_users',
      method: 'DELETE',
      data: {
        ids: ids
      }
    })
  }
  
  
  /**
   * 更新
   * @param {*} datas 
   * @returns 
   */
  export const updateAPI = (datas, id) => {
    return httpInstance({
      url: `/api/v1/admin/users/${id}`,
      method: "PUT",
      data:datas,
    })
  }
  
  export const createAPI = (datas) => {
    return httpInstance({
      url: '/api/v1/admin/users',
      method: "POST",
      data:datas,
    })
  }

