import { createApp } from 'vue'
import App from './App.vue'
import { createPinia } from 'pinia'
import router from './router'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'

// 引入初始化样式文件
import '@/styles/common.scss'


import 'ant-design-vue/dist/reset.css';



const pinia = createPinia()
pinia.use(piniaPluginPersistedstate)


const app = createApp(App)

app.use(pinia)
app.use(router)
// app.use(Antd)

app.mount('#app')
