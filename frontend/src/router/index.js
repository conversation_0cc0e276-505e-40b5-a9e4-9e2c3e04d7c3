import {
    createRouter,
    createWebHistory
}
from 'vue-router'
import Login from '@/views/admin/Login/index.vue'
import Home from '@/views/admin/Home/index.vue'
import User from '@/views/admin/User/index.vue'
import Statistics from '@/views/admin/Statistics/index.vue'
import Order from '@/views/admin/Order/index.vue'
import MemberShipPlan from '@/views/admin/MemberShipPlan/index.vue'



const routes = [{
    path: '/admin',
    component: Home,
    redirect: '/admin/statistics',
    children: [
        {
            path: 'statistics',
            name: 'statistics',
            component: Statistics
        },
        {
            path: 'user',
            name: 'user',
            component: User
        },
        {
            path: 'order',
            name: 'order',
            component: Order
        },
        {
            path: 'member_ship_plan',
            name: 'member_ship_plan',
            component: MemberShipPlan
        },
        {
            path: '/',
            redirect: '/admin/login'
        },

    ]
}, {
    path: '/admin/login',
    component: Login
}]


const router = createRouter({
    history: createWebHistory(),
    routes
})

//导出router
export default router