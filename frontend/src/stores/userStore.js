// 管理用户数据相关

import {
    defineStore
} from 'pinia'
import {
    ref
} from 'vue'

import {
    loginAPI
} from '@/apis/admin/user'


export const useUserStore = defineStore('user', () => {
    // 1. 定义管理用户数据的state
    const userInfo = ref({})
    // 2. 定义获取接口数据的action函数
    const getUserInfo = async ({
        email,
        password
    }) => {
        const res = await loginAPI({
            email,
            password
        })
        if (res.code === 200) {
            userInfo.value = res.data
        } else {
            throw new Error(res.msg)
        }
    
    }
    //  退出时清除用户信息
    const clearUserInfo = ()=>{
        userInfo.value = {}
    }
    // 3. 以对象的格式把state和action return
    return  {
        userInfo,
        getUserInfo,
        clearUserInfo
      }
    }, {
      persist: true,
    })