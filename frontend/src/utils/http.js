import axios from "axios";

import { message } from 'ant-design-vue';
import  router from '@/router'

import { useUserStore } from '@/stores/userStore'


const httpInstance = axios.create({
    baseURL: import.meta.env.VITE_API_URL,
    timeout: 5000
})

// 拦截器
// axios请求拦截器
httpInstance.interceptors.request.use(config => {
    // 1. 从pinia获取token数据
  const userStore = useUserStore()
  console.log(userStore.userInfo, 'userStore.userInfo')
  // 2. 按照后端的要求拼接token数据
  const access_token = userStore.userInfo.access_token
  console.log(access_token)
  if (access_token) {
    config.headers.Authorization = `Bearer ${access_token}`
  }
  return config
}, e => Promise.reject(e))

// axios响应式拦截器
httpInstance.interceptors.response.use(res => res.data, e => {
    const userStore = useUserStore()
    // 处理网络错误或超时
    if (!e.response) {
        message.error('网络错误或请求超时，请稍后重试');
        return Promise.reject(e);
    }

    // 401 token 失效处理
    if(e.response.status === 401){
        userStore.clearUserInfo()
        router.push('/admin/login')
        return Promise.reject(e);
    }

    // 500错误处理
    if(e.response.status === 500) {
        message.error(e.response.data?.msg || '服务器内部错误');
        return Promise.reject(e);
    }
    
    // 其他错误处理
    message.warning(e.response.data?.msg || "请求失败，请稍后重试");
    return Promise.reject(e);
})


export default httpInstance