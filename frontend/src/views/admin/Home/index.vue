<template>
  <a-layout style="min-height: 100vh">
    <a-layout-sider v-model:collapsed="collapsed" :trigger="null" collapsible>
      <div class="logo">
        <h1>灵象工具箱后台</h1>
      </div>
      <!-- 统计 -->
      <a-menu v-model:selectedKeys="selectedKeys" theme="dark" mode="inline" @click="handleClick">
        <a-menu-item key="statistics">
          <DashboardOutlined />
          <span>统计</span>
        </a-menu-item>
      </a-menu>

      <a-menu v-model:selectedKeys="selectedKeys" theme="dark" mode="inline" @click="handleClick">
        <a-menu-item key="user">
          <TeamOutlined />
          <span>用户管理</span>
        </a-menu-item>
      </a-menu>
      <a-menu v-model:selectedKeys="selectedKeys" theme="dark" mode="inline" @click="handleClick">
        <a-menu-item key="order">
          <TransactionOutlined />
          <span>订单管理</span>
        </a-menu-item>
      </a-menu>
      <a-menu v-model:selectedKeys="selectedKeys" theme="dark" mode="inline" @click="handleClick">
        <a-menu-item key="member_ship_plan">
          <CrownOutlined />
          <span>会员计划管理</span>
        </a-menu-item>
      </a-menu>
    </a-layout-sider>
    <a-layout id="components-layout-demo-custom-trigger">
      <a-layout-header style="background: #fff; padding: 0">
        <menu-unfold-outlined v-if="collapsed" class="trigger" @click="() => (collapsed = !collapsed)" />
        <menu-fold-outlined v-else class="trigger" @click="() => (collapsed = !collapsed)" />
        <div class="logout">
          <ul>
            <template v-if="userStore.userInfo.access_token">
              <li><a href="javascript:;"><i class="iconfont icon-user"></i>{{ userStore.userInfo.username }}</a></li>
              <li>
                <a-popconfirm title="确认退出吗?" ok-text="是" cancel-text="否" @confirm="confirm">
                  <a href="#">退出登录</a>
                </a-popconfirm>
              </li>
            </template>
          </ul>
        </div>
      </a-layout-header>
      <a-layout-content :style="{ margin: '24px 16px', padding: '24px', background: '#fff', minHeight: '280px' }">
        <RouterView />
      </a-layout-content>
    </a-layout>
  </a-layout>
</template>
<script setup>
import { ref, onMounted } from 'vue';
import { RouterView } from 'vue-router';
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '@/stores/userStore'
const router = useRouter()
const route = useRoute()

const userStore = useUserStore()
// 退出登录
const confirm = () => {
  userStore.clearUserInfo()
  console.log('退出')
  // 2.跳转到登录页
  router.push('/admin/login')
}

const selectedKeys = ref([]);
const collapsed = ref(false);

const handleClick = ({ item, key, keyPath }) => {
  console.log('点击路由===>', item, key, keyPath)
  selectedKeys.value = keyPath
  router.push({
    name: key,
  })
}
onMounted(()=>{
  selectedKeys.value = [route.name]
})

</script>
<style scoped lang='scss'>
.logo {
  margin: 16px;
  color: white;
  font-size: 17px;

}

#components-layout-demo-custom-trigger .trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color 0.3s;
}

#components-layout-demo-custom-trigger .trigger:hover {
  color: #1890ff;
}

#components-layout-demo-custom-trigger .logo {
  height: 32px;
  background: rgba(255, 255, 255, 0.3);
  margin: 16px;
}

.site-layout .site-layout-background {
  background: #fff;
}

.logout{
  float: right;
  margin-right: 100px;
  li {
    float: left;
    margin-right: 20px;
  }
}
</style>