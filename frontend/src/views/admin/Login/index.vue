<template>
  <div class="login-container">
    <div class="login-box">
      <div class="login-content">
        <h1 class="site-title">灵象工具箱后台</h1>
        <h2 class="login-title">账户登录</h2>
        
        <div class="form-container">
          <a-form ref="formRef" :model="form" :rules="rules" layout="vertical">
            <a-form-item name="email" label="邮箱">
              <a-input 
                v-model:value="form.email"
                placeholder="请输入邮箱"
                size="large"
                class="custom-input"
                autocomplete="username"
              />
            </a-form-item>
            <a-form-item name="password" label="密码">
              <a-input-password 
                v-model:value="form.password"
                placeholder="请输入密码"
                size="large"
                class="custom-input"
                autocomplete="current-password"
              />
            </a-form-item>

            <a-button 
              type="primary" 
              size="large" 
              class="login-button" 
              @click="doLogin"
              :loading="loading"
            >
              {{ loading ? '登录中...' : '登录' }}
            </a-button>
          </a-form>
        </div>
      </div>
      
      <footer class="login-footer">
        <div class="footer-links">
          <a href="javascript:;">关于我们</a>
          <span class="divider">|</span>
          <a href="javascript:;">帮助中心</a>
        </div>
        <p class="copyright">Copyright © {{ new Date().getFullYear() }} hubery. All rights reserved.</p>
      </footer>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { message } from 'ant-design-vue';
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/userStore'

const userStore = useUserStore()
const loading = ref(false)

// 1. 准备表单对象
const form = ref({
  email: '',
  password: '',
})

// 2. 准备规则对象
const rules = {
  email: [
    { required: true, message: '邮箱不能为空', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '密码不能为空', trigger: 'blur' },
    { min: 1, max: 40, message: '密码长度为1-20个字符', trigger: 'blur' },
  ]
}

// 3. 获取form实例做统一校验
const formRef = ref(null)
const router = useRouter()
const doLogin = () => {
  const { email, password } = form.value
  formRef.value
    .validate()
    .then(async () => {
      try {
        loading.value = true
        await userStore.getUserInfo({ email, password })
        message.success('登录成功')
        router.replace({ path: '/admin/statistics' })
      } catch (error) {
        message.error(error.message || '登录失败，请检查账号密码')
        console.error('登录失败:', error)
      } finally {
        loading.value = false
      }
    })
    .catch(() => {
      message.error('请填写正确的登录信息')
    });
}
</script>

<style scoped lang='scss'>
.login-container {
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #43cea2 0%, #185a9d 100%);
  padding: 20px;
}

.login-box {
  width: 100%;
  max-width: 420px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  padding: 40px;
}

.login-content {
  text-align: center;
}

.site-title {
  font-size: 36px;
  font-weight: 700;
  color: #185a9d;
  margin-bottom: 10px;
  font-family: 'Playfair Display', serif;
}

.login-title {
  font-size: 24px;
  color: #333;
  margin-bottom: 30px;
  font-weight: 500;
}

.form-container {
  margin-top: 20px;
}

.custom-input {
  height: 45px;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  transition: all 0.3s ease;
  
  &:hover, &:focus {
    border-color: #43cea2;
    box-shadow: 0 0 0 2px rgba(67, 206, 162, 0.1);
  }
}

.login-button {
  width: 100%;
  height: 45px;
  margin-top: 20px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  background: linear-gradient(to right, #43cea2, #185a9d);
  border: none;
  
  &:hover {
    opacity: 0.9;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(67, 206, 162, 0.2);
  }
}

.login-footer {
  margin-top: 40px;
  text-align: center;
  
  .footer-links {
    margin-bottom: 10px;
    
    a {
      color: #666;
      text-decoration: none;
      transition: color 0.3s ease;
      
      &:hover {
        color: #43cea2;
      }
    }
    
    .divider {
      margin: 0 10px;
      color: #ccc;
    }
  }
  
  .copyright {
    color: #999;
    font-size: 14px;
  }
}

// 响应式设计
@media (max-width: 480px) {
  .login-box {
    padding: 30px 20px;
  }
  
  .site-title {
    font-size: 30px;
  }
  
  .login-title {
    font-size: 20px;
  }
}
</style>