<template>
    <div class="table-operations">
        <a-space>
            <a-button type="primary" @click="handleAdd">新增</a-button>
            <a-input-search addon-before="名称" enter-button @search="onSearch" @change="onSearchChange" />
        </a-space>
    </div>
    <!-- 表格 -->
    <a-table :columns="columns" rowKey="id" :data-source="data" :pagination="pagination"
        :loading="loading" @change="handleTableChange">
        <template #bodyCell="{ column, text, record }">
            <template v-if="column.dataIndex === 'create_time' || column.dataIndex === 'update_time'">
                {{ text ? formatDate(text) : '-' }}
            </template>
            <template v-if="column.dataIndex === 'operation'">
                <span>
                    <a @click="handleEdit(record)">编辑</a>
                    <a-divider type="vertical" />
                    <a-popconfirm title="确定删除?" ok-text="是" cancel-text="否" @confirm="confirmDelete(record.id)">
                        <a href="#">删除</a>
                    </a-popconfirm>
                </span>
            </template>
        </template>
    </a-table>

    <!--弹窗区域-->
    <div>
        <a-modal :open="modal.visile" :forceRender="true" :title="modal.title" ok-text="确认" cancel-text="取消"
            @cancel="handleCancel" @ok="handleOk">
            <div>
                <a-form ref="myform" :label-col="{ style: { width: '100px' } }" :model="modal.form" :rules="modal.rules">
                    <a-row :gutter="24">
                        <a-col span="24">
                            <a-form-item label="计划名称" name="name">
                                <a-input placeholder="请输入" v-model:value="modal.form.name"></a-input>
                            </a-form-item>
                        </a-col>
                        <a-col span="24">
                            <a-form-item label="原价" name="original_price">
                                <a-input-number placeholder="请输入" v-model:value="modal.form.original_price" :min="0" :precision="2"></a-input-number>
                            </a-form-item>
                        </a-col>
                        <a-col span="24">
                            <a-form-item label="优惠价" name="discount_price">
                                <a-input-number placeholder="请输入" v-model:value="modal.form.discount_price" :min="0" :precision="2"></a-input-number>
                            </a-form-item>
                        </a-col>
                        <a-col span="24">
                            <a-form-item label="有效期(天)" name="duration_days">
                                <a-input-number placeholder="请输入" v-model:value="modal.form.duration_days" :min="1"></a-input-number>
                            </a-form-item>
                        </a-col>
                        <a-col span="24">
                            <a-form-item label="描述" name="description">
                                <a-textarea placeholder="请输入" v-model:value="modal.form.description"></a-textarea>
                            </a-form-item>
                        </a-col>
                        <a-col span="24">
                            <a-form-item label="状态" name="is_active">
                                <a-select placeholder="请选择" allowClear v-model:value="modal.form.is_active">
                                    <a-select-option value="true">启用</a-select-option>
                                    <a-select-option value="false">禁用</a-select-option>
                                </a-select>
                            </a-form-item>
                        </a-col>
                    </a-row>
                </a-form>
            </div>
        </a-modal>
    </div>
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue'
import { message } from 'ant-design-vue';
import { getListAPI, delleteAPI, updateAPI, createAPI } from '@/apis/admin/member_shep_plan'
import { formatDate } from '@/utils/utils'

const columns = [
    {
        title: 'Id',
        dataIndex: 'id',
        key: 'id',
    },
    {
        title: '名称',
        dataIndex: 'name',
        key: 'name',
    },
    {
        title: '原价',
        dataIndex: 'original_price',
        key: 'original_price',
        customRender: ({ text }) => `¥${text.toFixed(2)}`
    },
    {
        title: '优惠价',
        dataIndex: 'discount_price',
        key: 'discount_price',
        customRender: ({ text }) => `¥${text.toFixed(2)}`
    },
    {
        title: '有效期(天)',
        dataIndex: 'duration_days',
        key: 'duration_days',
    },
    {
        title: '描述',
        dataIndex: 'description',
        key: 'description',
    },
    {
        title: '状态',
        dataIndex: 'is_active',
        key: 'is_active',
        customRender: ({ text }) => text ? '启用' : '禁用'
    },
    {
        title: '创建时间',
        dataIndex: 'create_time',
        key: 'create_time'
    },
    {
        title: '更新时间',
        dataIndex: 'update_time',
        key: 'update_time'
    },
    {
        title: '操作',
        dataIndex: 'operation',
        key: 'operation'
    }
];

const loading = ref(false)
const pagination = ref({
    total: 0,
    current: 1,
    pageSize: 8,
});

const params = ref({
    page: 1,
    search: '',
    page_size: pagination.value.pageSize
})
const data = ref([])

const getList = async () => {
    loading.value = true
    try {
        const res = await getListAPI(params.value)
        data.value = res.data
        pagination.value.total = res.total
    } finally {
        loading.value = false
    }
}

// 弹窗数据源
const modal = reactive({
    visile: false,
    editFlag: false,
    title: '',
    form: {
        id: undefined,
        name: undefined,
        original_price: undefined,
        discount_price: undefined,
        duration_days: undefined,
        description: undefined,
        is_active: 'true',
    },
    rules: {
        name: [{ required: true, message: '请输入名称', trigger: 'change' }],
        original_price: [{ required: true, message: '请输入原价', trigger: 'change' }],
        discount_price: [{ required: true, message: '请输入优惠价', trigger: 'change' }],
        duration_days: [{ required: true, message: '请输入有效期', trigger: 'change' }],
    },
});

const myform = ref();

// 表格分页改变
const handleTableChange = (page, filters, sorter) => {
    pagination.value.current = page.current
    params.value.page = page.current
    pagination.value.pageSize = page.pageSize
    params.value.page_size = page.pageSize
    getList()
};

// 搜索相关方法
const onSearchChange = (e) => {
    params.value.search = e?.target?.value;
};

const onSearch = () => {
    params.value.page = 1;
    pagination.value.current = 1;
    getList()
};

// 恢复表单初始状态
const resetModal = () => {
    myform.value?.resetFields();
    modal.form = {
        id: undefined,
        name: undefined,
        original_price: undefined,
        discount_price: undefined,
        duration_days: undefined,
        description: undefined,
        is_active: 'true',
    };
};

// 关闭弹窗
const hideModal = () => {
    modal.visile = false;
};

// 取消
const handleCancel = () => {
    hideModal();
    resetModal();
};

// 编辑
const handleEdit = (record) => {
    resetModal();
    modal.visile = true;
    modal.editFlag = true;
    modal.title = '编辑会员计划';
    
    // 填充表单数据
    for (const key in record) {
        if (Object.keys(modal.form).includes(key)) {
            if (key === 'is_active') {
                modal.form[key] = record[key].toString();
            } else {
                modal.form[key] = record[key];
            }
        }
    }
};

// 新增
const handleAdd = () => {
    resetModal();
    modal.visile = true;
    modal.editFlag = false;
    modal.title = '新增会员计划';
};

// 提交表单
const handleOk = () => {
    myform.value.validate().then(() => {
        const formData = { ...modal.form };
        // 转换布尔值
        if (formData.is_active !== undefined) {
            formData.is_active = formData.is_active === 'true';
        }
        
        if (modal.editFlag) {
            // 编辑模式
            updateAPI(formData, formData.id).then((res) => {
                if (res.code === 200) {
                    message.success('更新成功');
                    hideModal();
                    getList();
                } else {
                    message.error(res.message || '更新失败');
                }
            }).catch((err) => {
                message.error('更新失败: ' + err.message);
            });
        } else {
            // 新增模式
            createAPI(formData).then((res) => {
                if (res.code === 200) {
                    message.success('创建成功');
                    hideModal();
                    getList();
                } else {
                    message.error(res.message || '创建失败');
                }
            }).catch((err) => {
                message.error('创建失败: ' + err.message);
            });
        }
    }).catch(error => {
        console.log('表单验证错误:', error);
    });
};

// 删除
const confirmDelete = id => {
    delleteAPI(id).then((res) => {
        if (res.code === 200) {
            message.success('删除成功');
            // 如果当前页只有一条数据，删除后跳转到上一页
            if (data.value.length === 1 && pagination.value.current > 1) {
                pagination.value.current -= 1;
                params.value.page = pagination.value.current;
            }
            getList();
        } else {
            message.error(res.message || '删除失败');
        }
    }).catch((err) => {
        message.error('删除失败: ' + err.message);
    });
};

onMounted(() => {
    getList()
})
</script>

<style lang="scss" scoped>
.table-operations {
    margin-bottom: 20px;
}
</style>
