<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能抠图</title>
    <style>
        :root {
            --primary-color: #10b981;
            --text-color: #1f2937;
            --bg-color: #ffffff;
            --section-bg: #f9fafb;
            --accent-color: #059669;
        }
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background: var(--bg-color);
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        header {
            background: var(--bg-color);
            padding: 20px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 100;
        }
        .nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: var(--primary-color);
            letter-spacing: -0.5px;
        }
        .hero {
            padding: 140px 0 80px;
            text-align: center;
            background: linear-gradient(135deg, #ecfdf5 0%, #f0fdfa 100%);
        }
        .hero h1 {
            font-size: 2.75rem;
            margin-bottom: 20px;
            color: var(--text-color);
            letter-spacing: -1px;
        }
        .hero p {
            font-size: 1.25rem;
            color: #4b5563;
            max-width: 800px;
            margin: 0 auto 30px;
        }
        .features {
            padding: 80px 0;
            background: var(--section-bg);
        }
        .section-title {
            text-align: center;
            margin-bottom: 50px;
            font-size: 2rem;
            color: var(--text-color);
            letter-spacing: -0.5px;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 40px;
            padding: 20px 0;
        }
        .feature-card {
            background: var(--bg-color);
            padding: 40px;
            border-radius: 16px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
            border: 1px solid rgba(0,0,0,0.05);
        }
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 12px rgba(0,0,0,0.1);
            border-color: var(--accent-color);
        }
        .feature-card h3 {
            color: var(--primary-color);
            margin-bottom: 15px;
            font-size: 1.5rem;
            letter-spacing: -0.5px;
        }
        .feature-card p {
            color: #4b5563;
            line-height: 1.7;
        }
        .cta-button {
            display: inline-block;
            background: var(--primary-color);
            color: white;
            padding: 14px 32px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            letter-spacing: 0.5px;
        }
        .cta-button:hover {
            background: var(--accent-color);
            transform: translateY(-2px);
        }
        footer {
            background: var(--text-color);
            color: #f3f4f6;
            padding: 40px 0;
            text-align: center;
        }
        .icp-info {
            margin-top: 20px;
            font-size: 14px;
        }
        .icp-info a {
            color: #f3f4f6;
            text-decoration: none;
            opacity: 0.8;
            transition: opacity 0.3s;
        }
        .icp-info a:hover {
            opacity: 1;
        }
        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2rem;
            }
            .hero p {
                font-size: 1.1rem;
            }
            .feature-grid {
                grid-template-columns: 1fr;
            }
            .section-title {
                font-size: 1.75rem;
            }
        }
        .pricing {
            padding: 80px 0;
            background: var(--bg-color);
        }
        .pricing-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            padding: 20px 0;
        }
        .pricing-card {
            background: var(--bg-color);
            padding: 40px;
            border-radius: 16px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
            border: 1px solid rgba(0,0,0,0.1);
            text-align: center;
        }
        .pricing-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 12px rgba(0,0,0,0.1);
        }
        .pricing-title {
            font-size: 1.5rem;
            color: var(--text-color);
            margin-bottom: 10px;
        }
        .pricing-subtitle {
            color: #6b7280;
            margin-bottom: 20px;
            font-size: 0.9rem;
        }
        .pricing-price {
            font-size: 2.5rem;
            color: #2563eb;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .pricing-period {
            color: #6b7280;
            font-size: 0.9rem;
            margin-bottom: 30px;
        }
        .pricing-features {
            list-style: none;
            margin: 0;
            padding: 0;
            text-align: left;
        }
        .pricing-features li {
            margin: 10px 0;
            color: #4b5563;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .pricing-features li::before {
            content: "✓";
            color: #2563eb;
            font-weight: bold;
        }
        .discount-tag {
            display: inline-block;
            background: #fee2e2;
            color: #ef4444;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            margin-left: 8px;
        }
        .original-price {
            text-decoration: line-through;
            color: #9ca3af;
            font-size: 1rem;
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <header>
        <div class="container nav">
            <div class="logo">抠图工具</div>
        </div>
    </header>

    <section class="hero">
        <div class="container">
            <h1>智能一键抠图</h1>
            <p>基于深度学习的智能抠图技术，快速移除图片背景，支持批量处理，效果精准自然</p>
            <a href="#features" class="cta-button">订阅使用</a>
        </div>
    </section>

    <section class="pricing">
        <div class="container">
            <h2 class="section-title">价格方案</h2>
            <div class="pricing-grid">
                <div class="pricing-card">
                    <h3 class="pricing-title">专业版（月度授权）</h3>
                    <p class="pricing-subtitle">体验使用</p>
                    <div>
                        <span class="original-price">¥9.9</span>
                        <span class="discount-tag">30% OFF</span>
                    </div>
                    <div class="pricing-price">¥6.9</div>
                    <div class="pricing-period">/月</div>
                    <ul class="pricing-features">
                        <li>支持视频去水印</li>
                        <li>支持视频抠图、自定义背景颜色和图片</li>
                        <li>支持AI抠图，单张、批量，更多模型选择</li>
                        <li>抠图支持更多模式，描边、换背景、裁剪</li>
                        <li>支持图片格式转换，单张、批量</li>
                        <li>支持高级截图美化</li>
                        <li>支持OCR识别，单张、批量</li>
                        <li>支持智能擦除，单张、批量</li>
                    </ul>
                </div>
                <div class="pricing-card">
                    <h3 class="pricing-title">专业版（年度授权）</h3>
                    <p class="pricing-subtitle">适合专业创作者使用</p>
                    <div>
                        <span class="original-price">¥99</span>
                        <span class="discount-tag">30% OFF</span>
                    </div>
                    <div class="pricing-price">¥69</div>
                    <div class="pricing-period">/年</div>
                    <ul class="pricing-features">
                        <li>支持视频去水印</li>
                        <li>支持视频抠图、自定义背景颜色和图片</li>
                        <li>支持AI抠图，单张、批量，更多模型选择</li>
                        <li>抠图支持更多模式，描边、换背景、裁剪</li>
                        <li>支持图片格式转换，单张、批量</li>
                        <li>支持高级截图美化</li>
                        <li>支持OCR识别，单张、批量</li>
                        <li>支持智能擦除，单张、批量</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <section id="features" class="features">
        <div class="container">
            <h2 class="section-title">核心功能</h2>
            <div class="feature-grid">
                <div class="feature-card">
                    <h3>智能抠图</h3>
                    <p>先进的AI算法自动识别主体，一键去除背景，支持人像、商品、动物等多种场景</p>
                </div>
                <div class="feature-card">
                    <h3>批量处理</h3>
                    <p>支持多张图片同时处理，提高工作效率，适合电商产品、设计素材等批量需求</p>
                </div>
                <div class="feature-card">
                    <h3>自定义背景</h3>
                    <p>支持透明背景导出，可自定义背景颜色或图片，满足不同场景的应用需求</p>
                </div>
            </div>
        </div>
    </section>

    <footer>
        <div class="container">
            <p>© 2024 AI智能抠图工具</p>
            <div class="icp-info">
                <a href="https://beian.miit.gov.cn/" target="_blank">备案号：豫ICP备2025127282号</a>
            </div>
        </div>
    </footer>
</body>
</html> 